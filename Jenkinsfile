pipeline {
    agent any
    
    // Define environment variables
    environment {
        BASE_URI = credentials('api-base-uri')
        TOKEN = credentials('api-token')
        MAVEN_OPTS = '-Dmaven.repo.local=.m2/repository'
    }
    
    // Define build triggers
    triggers {
        // Poll SCM every 5 minutes for changes
        pollSCM('H/5 * * * *')
        // Run daily at 2 AM
        cron('0 2 * * *')
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo '📥 Checking out code from repository'
                checkout scm
            }
        }
        
        stage('Setup Java') {
            steps {
                echo '☕ Setting up Java environment'
                // <PERSON> will use the configured JDK
                sh 'java -version'
                sh 'mvn -version'
            }
        }
        
        stage('Validate Configuration') {
            steps {
                echo '🔍 Validating test configuration'
                script {
                    if (!env.BASE_URI) {
                        error('❌ BASE_URI credential is not configured')
                    }
                    if (!env.TOKEN) {
                        error('❌ TOKEN credential is not configured')
                    }
                    echo '✅ All required credentials are configured'
                }
            }
        }
        
        stage('Run API Tests') {
            steps {
                echo "🚀 Running API tests against: ${env.BASE_URI}"
                sh '''
                    mvn clean test -Dtest=NewTest \
                        -DBASE_URI="${BASE_URI}" \
                        -DTOKEN="${TOKEN}"
                '''
            }
            post {
                always {
                    // Publish test results
                    publishTestResults testResultsPattern: 'target/surefire-reports/*.xml'
                    
                    // Archive test reports
                    archiveArtifacts artifacts: 'target/surefire-reports/**/*', 
                                   fingerprint: true,
                                   allowEmptyArchive: true
                }
            }
        }
    }
    
    post {
        always {
            echo '🧹 Cleaning up workspace'
            cleanWs()
        }
        success {
            echo '✅ All tests passed successfully!'
            // Send success notification
            emailext (
                subject: "✅ API Tests Passed - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: "All API tests passed successfully!\n\nBuild: ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        failure {
            echo '❌ Tests failed!'
            // Send failure notification
            emailext (
                subject: "❌ API Tests Failed - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: "API tests failed. Please check the build logs.\n\nBuild: ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
