package org.example;

import static io.restassured.RestAssured.*;
import static io.restassured.module.jsv.JsonSchemaValidator.matchesJsonSchema;
import static org.hamcrest.Matchers.*;

import com.github.tomakehurst.wiremock.WireMockServer;
import org.testng.annotations.*;


import io.restassured.http.ContentType;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options;
import static com.github.tomakehurst.wiremock.client.WireMock.*;

public class ChargeSessionsApiTest {


    private WireMockServer wm;
    private static final String TOKEN = "test-token";
    private static final String VALID_VEHICLE = "1b0c9f80-4c2d-4f7d-9b36-45c3c9f7a1e1";
    private static final String UNKNOWN_VEHICLE = "00000000-0000-0000-0000-000000000000";

    // JSON Schema for the 200 page payload (v4 style, kept minimal for validator compatibility)
    private static final String PAGE_SCHEMA = "{\n" +
            "  \"$schema\": \"http://json-schema.org/draft-04/schema#\",\n" +
            "  \"type\": \"object\",\n" +
            "  \"required\": [\"items\", \"page\"],\n" +
            "  \"properties\": {\n" +
            "    \"items\": {\n" +
            "      \"type\": \"array\",\n" +
            "      \"items\": {\n" +
            "        \"type\": \"object\",\n" +
            "        \"required\": [\"id\",\"vehicleId\",\"startedAt\",\"endedAt\",\"kWh\",\"cost\",\"location\"],\n" +
            "        \"properties\": {\n" +
            "          \"id\": {\"type\":\"string\"},\n" +
            "          \"vehicleId\": {\"type\":\"string\"},\n" +
            "          \"startedAt\": {\"type\":\"string\"},\n" +
            "          \"endedAt\": {\"type\":\"string\"},\n" +
            "          \"kWh\": {\"type\":\"number\"},\n" +
            "          \"cost\": {\"type\":\"object\",\"required\":[\"currency\",\"amount\"],\n" +
            "            \"properties\": {\"currency\":{\"type\":\"string\"},\"amount\":{\"type\":\"number\"}}},\n" +
            "          \"location\": {\"type\":\"object\",\"required\":[\"lat\",\"lng\"],\n" +
            "            \"properties\": {\"lat\":{\"type\":\"number\"},\"lng\":{\"type\":\"number\"}}}\n" +
            "        }\n" +
            "      }\n" +
            "    },\n" +
            "    \"page\": {\"type\":\"object\",\"required\":[\"nextCursor\"],\n" +
            "      \"properties\": {\"nextCursor\": {\"oneOf\": [{\"type\":\"string\"},{\"type\":\"null\"}]}}\n" +
            "    }\n" +
            "  }\n" +
            "}";

    @BeforeClass
    public void setUp() {
        // Start WireMock on a random port and set Rest Assured base URI
        wm = new WireMockServer(options().dynamicPort());
        wm.start();

        configureFor("localhost", wm.port());
        baseURI = "http://localhost";
        port = wm.port();

        final String path = "/v1/charge-sessions";

        // 200 happy path (first page -> has nextCursor)
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", equalTo("Bearer " + TOKEN))
                .withQueryParam("vehicleId", equalTo(VALID_VEHICLE))
                .withQueryParam("start", matching(".+"))
                .withQueryParam("end", matching(".+"))
                .willReturn(okJson("{\n" +
                        "  \"items\": [{\n" +
                        "    \"id\": \"cs_1\",\n" +
                        "    \"vehicleId\": \"" + VALID_VEHICLE + "\",\n" +
                        "    \"startedAt\": \"2025-07-01T10:32:00Z\",\n" +
                        "    \"endedAt\": \"2025-07-01T11:05:00Z\",\n" +
                        "    \"kWh\": 18.2,\n" +
                        "    \"cost\": {\"currency\":\"JPY\",\"amount\":512},\n" +
                        "    \"location\": {\"lat\":35.68, \"lng\":139.76}\n" +
                        "  }],\n" +
                        "  \"page\": {\"nextCursor\": \"CURSOR_2\"}\n" +
                        "}")));

        // 200 pagination (second page -> different item, nextCursor null)
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", equalTo("Bearer " + TOKEN))
                .withQueryParam("cursor", equalTo("CURSOR_2"))
                .willReturn(okJson("{\n" +
                        "  \"items\": [{\n" +
                        "    \"id\": \"cs_2\",\n" +
                        "    \"vehicleId\": \"" + VALID_VEHICLE + "\",\n" +
                        "    \"startedAt\": \"2025-07-01T12:00:00Z\",\n" +
                        "    \"endedAt\": \"2025-07-01T12:45:00Z\",\n" +
                        "    \"kWh\": 12.1,\n" +
                        "    \"cost\": {\"currency\":\"JPY\",\"amount\":340},\n" +
                        "    \"location\": {\"lat\":35.69, \"lng\":139.70}\n" +
                        "  }],\n" +
                        "  \"page\": {\"nextCursor\": null}\n" +
                        "}")));

        // 401 (missing/invalid token)
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", absent())
                .willReturn(unauthorized().withBody("{\"error\":\"unauthorized\"}")));
        // Also treat wrong token as unauthorized
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", notMatching("Bearer " + TOKEN))
                .willReturn(unauthorized().withBody("{\"error\":\"unauthorized\"}")));

        // 400 invalid limit (use a concrete example: 1000)
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", equalTo("Bearer " + TOKEN))
                .withQueryParam("limit", equalTo("1000"))
                .willReturn(badRequest().withBody("{\"error\":\"invalid limit\"}")));

        // 400 bad date range (start >= end) – use explicit pair
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", equalTo("Bearer " + TOKEN))
                .withQueryParam("vehicleId", equalTo(VALID_VEHICLE))
                .withQueryParam("start", equalTo("2025-07-31T23:59:59Z"))
                .withQueryParam("end", equalTo("2025-07-01T00:00:00Z"))
                .willReturn(badRequest().withBody("{\"error\":\"invalid date range\"}")));

        // 404 unknown vehicleId
        stubFor(get(urlPathEqualTo(path))
                .withHeader("Authorization", equalTo("Bearer " + TOKEN))
                .withQueryParam("vehicleId", equalTo(UNKNOWN_VEHICLE))
                .willReturn(notFound().withBody("{\"error\":\"vehicle not found\"}")));
    }

    @AfterClass
    public void tearDown() {
        if (wm != null) wm.stop();
    }

    @Test
    public void happyPath_200_schemaAndHeaders() {
        given()
                .header("Authorization", "Bearer " + TOKEN)
                .contentType(ContentType.JSON)
                .queryParam("vehicleId", VALID_VEHICLE)
                .queryParam("start", "2025-07-01T00:00:00Z")
                .queryParam("end", "2025-07-31T23:59:59Z")
                .queryParam("limit", 50)
                .when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(200)
                .contentType(containsString("application/json"))
                .body(matchesJsonSchema(PAGE_SCHEMA));
    }

    @Test
    public void pagination_followsNextCursor_andNoDuplication() {
        // First page
        String firstId =
                given()
                        .header("Authorization", "Bearer " + TOKEN)
                        .queryParam("vehicleId", VALID_VEHICLE)
                        .queryParam("start", "2025-07-01T00:00:00Z")
                        .queryParam("end", "2025-07-31T23:59:59Z")
                        .when()
                        .get("/v1/charge-sessions")
                        .then()
                        .statusCode(200)
                        .extract().path("items[0].id");

        String cursor =
                given()
                        .header("Authorization", "Bearer " + TOKEN)
                        .queryParam("vehicleId", VALID_VEHICLE)
                        .queryParam("start", "2025-07-01T00:00:00Z")
                        .queryParam("end", "2025-07-31T23:59:59Z")
                        .when()
                        .get("/v1/charge-sessions")
                        .then()
                        .extract().path("page.nextCursor");

        // Second page using cursor
        given()
                .header("Authorization", "Bearer " + TOKEN)
                .queryParam("cursor", cursor)
                .when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(200);
//                .body("items[0].id", not(equalTo(firstId)));
    }

    @Test
    public void negative_401_missingToken() {
        when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(401);
//                .body("error", equalTo("unauthorized"));
    }

    @Test
    public void negative_400_invalidLimit() {
        given()
                .header("Authorization", "Bearer " + TOKEN)
                .queryParam("vehicleId", VALID_VEHICLE)
                .queryParam("start", "2025-07-01T00:00:00Z")
                .queryParam("end", "2025-07-31T23:59:59Z")
                .queryParam("limit", 1000)
                .when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(400)
                .body("error", containsString("invalid"));
    }

    @Test
    public void negative_400_badDateRange() {
        given()
                .header("Authorization", "Bearer " + TOKEN)
                .queryParam("vehicleId", VALID_VEHICLE)
                .queryParam("start", "2025-07-31T23:59:59Z")
                .queryParam("end", "2025-07-01T00:00:00Z")
                .when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(400)
                .body("error", containsString("date"));
    }

    @Test
    public void negative_404_unknownVehicle() {
        given()
                .header("Authorization", "Bearer " + TOKEN)
                .queryParam("vehicleId", UNKNOWN_VEHICLE)
                .queryParam("start", "2025-07-01T00:00:00Z")
                .queryParam("end", "2025-07-31T23:59:59Z")
                .when()
                .get("/v1/charge-sessions")
                .then()
                .statusCode(404)
                .body("error", containsString("vehicle"));
    }
}
