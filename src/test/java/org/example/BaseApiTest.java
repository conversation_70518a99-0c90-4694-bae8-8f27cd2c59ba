package org.example;

import io.restassured.RestAssured;
import org.testng.SkipException;
import org.testng.annotations.BeforeSuite;

public class BaseApiTest {
    protected static String BASE_URI;
    protected static String BASE_PATH;
    protected static String TOKEN;
    protected static String VEHICLE_ID;
    protected static String UNKNOWN_VEHICLE_ID;

    @BeforeSuite
    public void setupSuite() {
        BASE_URI = prop("BASE_URI");
        TOKEN = prop("TOKEN");
        VEHICLE_ID = prop("VEHICLE_ID");
        BASE_PATH = prop("BASE_PATH", "/v1/charge-sessions");
        UNKNOWN_VEHICLE_ID = prop("UNKNOWN_VEHICLE_ID", "00000000-0000-0000-0000-000000000000");

        if (isBlank(BASE_URI) || isBlank(TOKEN) || isBlank(VEHICLE_ID)) {
            throw new SkipException("Set BASE_URI, TOKEN, VEHICLE_ID to run against a real API");
        }

        RestAssured.baseURI = BASE_URI;
    }

    protected static String prop(String key) {
        String v = System.getProperty(key, System.getenv(key));
        return v != null ? v.trim() : null;
    }
    protected static String prop(String key, String def) {
        String v = prop(key);
        return isBlank(v) ? def : v;
    }
    protected static boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }
}
