// src/test/java/interview/restassured/CatalogIT.java
package org.example;

import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.filter.Filter;
import io.restassured.filter.FilterContext;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.FilterableRequestSpecification;
import io.restassured.specification.FilterableResponseSpecification;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;

public class NewTest {

    String BASE_URI = prop("BASE_URI");
    String TOKEN    = prop("TOKEN");

    static String prop(String k){ String v = System.getProperty(k, System.getenv(k)); return v==null?null:v.trim(); }

    @BeforeClass
    public void setup(){
        if (BASE_URI == null || BASE_URI.isBlank()) throw new SkipException("Set BASE_URI");
        if (TOKEN == null || TOKEN.isBlank()) throw new SkipException("Set TOKEN");
        RestAssured.baseURI = BASE_URI;
    }

    // ---- reusable spec ----
    private io.restassured.specification.RequestSpecification apiSpec() {
        return new RequestSpecBuilder()
                .setBaseUri(BASE_URI)
                .addHeader("Authorization", "Bearer " + TOKEN)
                .setContentType(ContentType.JSON)
                .setAccept(ContentType.JSON)
                .log(io.restassured.filter.log.LogDetail.ALL) // or .setConfig with log.ifValidationFails
                .build();
    }
    @Test
    public void post_item_success_and_negative() {
        // success path
        given()
                .spec(apiSpec())
                .body(Map.of("name", "Adapter", "price", Map.of("amount", 199.0)))
                .when()
                .post("/v1/items")
                .then()
                .statusCode(anyOf(is(201), is(200)))
                .body("id", notNullValue());

        // negative path
        given()
                .spec(apiSpec())
                .body(Map.of("name", "", "price", Map.of("amount", 10.0)))
                .when()
                .post("/v1/items")
                .then()
                .statusCode(400);
    }
}
