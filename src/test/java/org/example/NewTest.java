// src/test/java/interview/restassured/CatalogIT.java
package org.example;

import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.filter.Filter;
import io.restassured.filter.FilterContext;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.FilterableRequestSpecification;
import io.restassured.specification.FilterableResponseSpecification;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;

public class NewTest {

    String BASE_URI = prop("BASE_URI");
    String TOKEN    = prop("TOKEN");

    static String prop(String k){ String v = System.getProperty(k, System.getenv(k)); return v==null?null:v.trim(); }

    @BeforeClass
    public void setup(){
        if (BASE_URI == null || BASE_URI.isBlank()) throw new SkipException("Set BASE_URI");
        if (TOKEN == null || TOKEN.isBlank()) throw new SkipException("Set TOKEN");
        RestAssured.baseURI = BASE_URI;
    }

    // ---- reusable spec ----
    private io.restassured.specification.RequestSpecification apiSpec() {
        return new RequestSpecBuilder()
                .setBaseUri(BASE_URI)
                .addHeader("Authorization", "Bearer " + TOKEN)
                .setContentType(ContentType.JSON)
                .setAccept(ContentType.JSON)
                .log(io.restassured.filter.log.LogDetail.ALL) // or .setConfig with log.ifValidationFails
                .build();
    }

    // ---- filter: retry once on 429 honoring Retry-After ----
    static class RetryAfterOnceFilter implements Filter {
        private final Duration minDelay = Duration.ofSeconds(1);

        @Override
        public Response filter(FilterableRequestSpecification req,
                               FilterableResponseSpecification res,
                               FilterContext ctx) {
            Response r = ctx.next(req, res);
            if (r.statusCode() == 429) {
                int ra = parseIntSafe(r.getHeader("Retry-After"), 1);
                sleepMillis(Math.max(minDelay.toMillis(), ra * 1000L));
                // retry once
                return ctx.next(req, res);
            }
            return r;
        }

        private static int parseIntSafe(String s, int d){ try { return Integer.parseInt(Objects.toString(s,"")); } catch(Exception e){ return d; } }
        private static void sleepMillis(long ms){ try { Thread.sleep(ms);} catch (InterruptedException ignored){} }
    }

    // ---- tests ----

    @Test
    public void get_items_with_retry_filter() {
        Response r = given()
                .spec(apiSpec())
                .filter(new RetryAfterOnceFilter())
                .when()
                .get("/v1/items?category=EV&limit=50");

        r.then()
                .statusCode(200)
                .contentType(containsString("application/json"))
                .body("items", notNullValue())
                .body("items.size()", greaterThanOrEqualTo(0))
                .body("items[0].id", anyOf(nullValue(), instanceOf(String.class)))
                .body("items[0].price.amount", anyOf(nullValue(), instanceOf(Number.class), greaterThanOrEqualTo(0)));

        String firstId = r.path("items[0].id");
        System.out.println("First item id: " + firstId);
    }

    @Test
    public void post_item_success_and_negative() {
        // success path
        given()
                .spec(apiSpec())
                .filter(new RetryAfterOnceFilter())
                .body(Map.of("name", "Adapter", "price", Map.of("amount", 199.0)))
                .when()
                .post("/v1/items")
                .then()
                .statusCode(anyOf(is(201), is(200)))
                .body("id", notNullValue());

        // negative path
        given()
                .spec(apiSpec())
                .body(Map.of("name", "", "price", Map.of("amount", 10.0)))
                .when()
                .post("/v1/items")
                .then()
                .statusCode(400);
    }
}
