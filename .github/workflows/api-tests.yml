name: API Tests

# When should this workflow run?
on:
  push:
    branches: [ main ]  # Run when code is pushed to main branch
  pull_request:
    branches: [ main ]  # Run when someone creates a pull request to main
  workflow_dispatch:    # Allow manual trigger from GitHub UI

jobs:
  test:
    runs-on: ubuntu-latest  # Use Ubuntu virtual machine

    steps:
    # Step 1: Get the code from repository
    - name: Checkout code
      uses: actions/checkout@v4

    # Step 2: Install Java 21
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        cache: maven  # Cache Maven dependencies for faster builds

    # Step 3: Run the tests
    - name: Run API Tests
      env:
        BASE_URI: ${{ secrets.BASE_URI }}  # Get BASE_URI from GitHub secrets
        TOKEN: ${{ secrets.TOKEN }}        # Get TOKEN from GitHub secrets
      run: mvn clean test -Dtest=NewTest

    # Step 4: Save test results (runs even if tests fail)
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()  # Always run this step, even if previous steps failed
      with:
        name: test-results
        path: target/surefire-reports/
