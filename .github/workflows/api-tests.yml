name: API Tests

# Trigger the workflow
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual trigger

jobs:
  api-tests:
    runs-on: ubuntu-latest
    
    steps:
    # Checkout the code
    - name: Checkout repository
      uses: actions/checkout@v4
    
    # Setup Java
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        cache: maven
    
    # Validate secrets are set
    - name: Validate configuration
      run: |
        if [ -z "${{ secrets.BASE_URI }}" ]; then
          echo "❌ BASE_URI secret is not set"
          exit 1
        fi
        if [ -z "${{ secrets.TOKEN }}" ]; then
          echo "❌ TOKEN secret is not set"
          exit 1
        fi
        echo "✅ All required secrets are configured"

    # Run tests with environment variables
    - name: Run API Tests
      env:
        BASE_URI: ${{ secrets.BASE_URI }}
        TOKEN: ${{ secrets.TOKEN }}
      run: |
        echo "🚀 Running API tests against: $BASE_URI"
        mvn clean test -Dtest=NewTest -Dmaven.test.failure.ignore=true

    # Run WireMock tests as well
    - name: Run WireMock Tests
      run: |
        echo "🧪 Running WireMock tests"
        mvn test -Dtest=ChargeSessionsApiTest
    
    # Upload test results
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          target/surefire-reports/
          target/test-classes/
    
    # Publish test results (optional)
    - name: Publish Test Results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: API Test Results
        path: target/surefire-reports/*.xml
        reporter: java-junit
        fail-on-error: true

    # Notify on failure (optional)
    - name: Notify on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ API Tests failed on ${{ github.ref }}"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Optional: Run tests against multiple environments
  multi-environment-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [staging, production]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        cache: maven
    
    - name: Run tests against ${{ matrix.environment }}
      env:
        BASE_URI: ${{ secrets[format('BASE_URI_{0}', matrix.environment)] }}
        TOKEN: ${{ secrets[format('TOKEN_{0}', matrix.environment)] }}
      run: |
        echo "Running tests against ${{ matrix.environment }}"
        mvn clean test -Dtest=NewTest
